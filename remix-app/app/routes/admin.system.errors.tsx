import { json } from '@remix-run/node';
import type { ActionFunctionArgs, LoaderFunctionArgs } from '@remix-run/node';
import { Form, useActionData, useLoaderData, useNavigation } from '@remix-run/react';
import { useUser } from '~/utils/client-utils';
import { useState } from 'react';

// Mock Sentry API client - in a real implementation, you would use the Sentry SDK
// or create a proper API client to interact with the Sentry API
class SentryApiClient {
  private apiToken: string;
  private organizationSlug: string;
  private projectSlug: string;
  
  constructor() {
    this.apiToken = process.env.SENTRY_API_TOKEN || 'mock-token';
    this.organizationSlug = process.env.SENTRY_ORG || 'organization';
    this.projectSlug = process.env.SENTRY_PROJECT || 'project';
  }
  
  // In a real implementation, this would make actual API calls to Sentry
  async getIssues() {
    // Mock data for demonstration purposes
    return [
      {
        id: 'issue1',
        title: 'TypeError: Cannot read property "x" of undefined',
        culprit: 'app/routes/search.tsx',
        level: 'error',
        status: 'unresolved',
        firstSeen: '2023-06-15T10:30:00Z',
        lastSeen: '2023-06-15T14:45:00Z',
        count: 42,
        userCount: 15,
        assignedTo: null,
        permalink: 'https://sentry.io/organizations/org/issues/issue1/',
      },
      {
        id: 'issue2',
        title: 'ReferenceError: fetch is not defined',
        culprit: 'app/routes/api.incidents.tsx',
        level: 'error',
        status: 'resolved',
        firstSeen: '2023-06-10T08:20:00Z',
        lastSeen: '2023-06-10T09:15:00Z',
        count: 3,
        userCount: 2,
        assignedTo: '<EMAIL>',
        permalink: 'https://sentry.io/organizations/org/issues/issue2/',
      },
      {
        id: 'issue3',
        title: 'Failed to fetch: Network error',
        culprit: 'app/routes/dashboard.tsx',
        level: 'warning',
        status: 'unresolved',
        firstSeen: '2023-06-14T16:40:00Z',
        lastSeen: '2023-06-15T17:30:00Z',
        count: 28,
        userCount: 12,
        assignedTo: null,
        permalink: 'https://sentry.io/organizations/org/issues/issue3/',
      }
    ];
  }
  
  async getIssueDetails(issueId: string) {
    // Mock data for demonstration purposes
    const issues = {
      issue1: {
        id: 'issue1',
        title: 'TypeError: Cannot read property "x" of undefined',
        culprit: 'app/routes/search.tsx',
        level: 'error',
        status: 'unresolved',
        firstSeen: '2023-06-15T10:30:00Z',
        lastSeen: '2023-06-15T14:45:00Z',
        count: 42,
        userCount: 15,
        assignedTo: null,
        permalink: 'https://sentry.io/organizations/org/issues/issue1/',
        events: [
          {
            id: 'event1',
            timestamp: '2023-06-15T14:45:00Z',
            user: {
              id: 'user123',
              email: '<EMAIL>',
              ip_address: '***********'
            },
            tags: {
              browser: 'Chrome',
              device: 'Desktop',
              os: 'Windows'
            },
            exception: {
              values: [
                {
                  type: 'TypeError',
                  value: 'Cannot read property "x" of undefined',
                  stacktrace: {
                    frames: [
                      {
                        filename: 'app/routes/search.tsx',
                        function: 'handleSearch',
                        lineno: 42,
                        colno: 15
                      },
                      {
                        filename: 'app/routes/search.tsx',
                        function: 'SearchPage',
                        lineno: 30,
                        colno: 10
                      }
                    ]
                  }
                }
              ]
            }
          }
        ]
      },
      issue2: {
        id: 'issue2',
        title: 'ReferenceError: fetch is not defined',
        culprit: 'app/routes/api.incidents.tsx',
        level: 'error',
        status: 'resolved',
        firstSeen: '2023-06-10T08:20:00Z',
        lastSeen: '2023-06-10T09:15:00Z',
        count: 3,
        userCount: 2,
        assignedTo: '<EMAIL>',
        permalink: 'https://sentry.io/organizations/org/issues/issue2/',
        events: [
          {
            id: 'event2',
            timestamp: '2023-06-10T09:15:00Z',
            user: {
              id: 'user456',
              email: '<EMAIL>',
              ip_address: '***********'
            },
            tags: {
              browser: 'Firefox',
              device: 'Desktop',
              os: 'MacOS'
            },
            exception: {
              values: [
                {
                  type: 'ReferenceError',
                  value: 'fetch is not defined',
                  stacktrace: {
                    frames: [
                      {
                        filename: 'app/routes/api.incidents.tsx',
                        function: 'fetchIncidents',
                        lineno: 15,
                        colno: 8
                      },
                      {
                        filename: 'app/routes/api.incidents.tsx',
                        function: 'loader',
                        lineno: 10,
                        colno: 5
                      }
                    ]
                  }
                }
              ]
            }
          }
        ]
      },
      issue3: {
        id: 'issue3',
        title: 'Failed to fetch: Network error',
        culprit: 'app/routes/dashboard.tsx',
        level: 'warning',
        status: 'unresolved',
        firstSeen: '2023-06-14T16:40:00Z',
        lastSeen: '2023-06-15T17:30:00Z',
        count: 28,
        userCount: 12,
        assignedTo: null,
        permalink: 'https://sentry.io/organizations/org/issues/issue3/',
        events: [
          {
            id: 'event3',
            timestamp: '2023-06-15T17:30:00Z',
            user: {
              id: 'user789',
              email: '<EMAIL>',
              ip_address: '***********'
            },
            tags: {
              browser: 'Safari',
              device: 'Mobile',
              os: 'iOS'
            },
            exception: {
              values: [
                {
                  type: 'Error',
                  value: 'Failed to fetch: Network error',
                  stacktrace: {
                    frames: [
                      {
                        filename: 'app/routes/dashboard.tsx',
                        function: 'fetchDashboardData',
                        lineno: 78,
                        colno: 22
                      },
                      {
                        filename: 'app/routes/dashboard.tsx',
                        function: 'DashboardPage',
                        lineno: 65,
                        colno: 18
                      }
                    ]
                  }
                }
              ]
            }
          }
        ]
      }
    };
    
    return issues[issueId as keyof typeof issues] || null;
  }
  
  async updateIssueStatus(issueId: string, status: string) {
    // In a real implementation, this would make an API call to update the issue status
    console.log(`Updating issue ${issueId} status to ${status}`);
    return { success: true };
  }
  
  async assignIssue(issueId: string, assignee: string | null) {
    // In a real implementation, this would make an API call to assign the issue
    console.log(`Assigning issue ${issueId} to ${assignee || 'no one'}`);
    return { success: true };
  }
}

// Create a singleton instance of the Sentry API client
const sentryClient = new SentryApiClient();

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const issueId = url.searchParams.get('issueId');
  
  if (issueId) {
    const issueDetails = await sentryClient.getIssueDetails(issueId);
    return json({ issueDetails });
  }
  
  const issues = await sentryClient.getIssues();
  return json({ issues });
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const formData = await request.formData();
  const action = formData.get('action')?.toString();
  const issueId = formData.get('issueId')?.toString();
  
  if (!issueId) {
    return json({ success: false, error: 'Issue ID is required' });
  }
  
  if (action === 'resolve') {
    await sentryClient.updateIssueStatus(issueId, 'resolved');
    return json({ success: true, message: `Issue ${issueId} has been resolved` });
  }
  
  if (action === 'unresolve') {
    await sentryClient.updateIssueStatus(issueId, 'unresolved');
    return json({ success: true, message: `Issue ${issueId} has been unresolved` });
  }
  
  if (action === 'assign') {
    const assignee = formData.get('assignee')?.toString() || null;
    await sentryClient.assignIssue(issueId, assignee);
    return json({ 
      success: true, 
      message: assignee 
        ? `Issue ${issueId} has been assigned to ${assignee}` 
        : `Issue ${issueId} has been unassigned` 
    });
  }
  
  return json({ success: false, error: 'Invalid action' });
};

export default function ErrorLogsPage() {
  const user = useUser();
  if (user.role !== 'Admin') {
    throw new Error('No permission.');
  }
  
  const data = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const [selectedIssueId, setSelectedIssueId] = useState<string | null>(null);
  const [assigneeInput, setAssigneeInput] = useState<string>('');
  
  const isSubmitting = navigation.state === 'submitting';
  
  // Function to get level badge color
  const getLevelColor = (level: string) => {
    switch (level) {
      case 'error':
        return 'bg-red-100 text-red-800';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800';
      case 'info':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  // Function to get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'resolved':
        return 'bg-green-100 text-green-800';
      case 'unresolved':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };
  
  return (
    <div className="p-8">
      <h1 className="text-3xl font-bold mb-8">Error Logs</h1>
      
      {actionData?.message && (
        <div className={`p-4 mb-4 rounded ${actionData.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
          {actionData.message}
          {actionData.error && (
            <div className="mt-2 text-sm">
              <strong>Error:</strong> {actionData.error}
            </div>
          )}
        </div>
      )}
      
      {data.issueDetails ? (
        // Issue details view
        <div>
          <div className="mb-4">
            <button
              onClick={() => setSelectedIssueId(null)}
              className="px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50"
            >
              Back to Issues
            </button>
          </div>
          
          <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-8">
            <div className="px-4 py-5 sm:px-6">
              <h2 className="text-xl font-semibold">{data.issueDetails.title}</h2>
              <div className="mt-2 flex flex-wrap gap-2">
                <span className={`px-2 py-1 text-xs rounded-full ${getLevelColor(data.issueDetails.level)}`}>
                  {data.issueDetails.level}
                </span>
                <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(data.issueDetails.status)}`}>
                  {data.issueDetails.status}
                </span>
              </div>
            </div>
            
            <div className="border-t border-gray-200 px-4 py-5 sm:px-6">
              <dl className="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2">
                <div className="sm:col-span-1">
                  <dt className="text-sm font-medium text-gray-500">First Seen</dt>
                  <dd className="mt-1 text-sm text-gray-900">{formatDate(data.issueDetails.firstSeen)}</dd>
                </div>
                <div className="sm:col-span-1">
                  <dt className="text-sm font-medium text-gray-500">Last Seen</dt>
                  <dd className="mt-1 text-sm text-gray-900">{formatDate(data.issueDetails.lastSeen)}</dd>
                </div>
                <div className="sm:col-span-1">
                  <dt className="text-sm font-medium text-gray-500">Occurrences</dt>
                  <dd className="mt-1 text-sm text-gray-900">{data.issueDetails.count}</dd>
                </div>
                <div className="sm:col-span-1">
                  <dt className="text-sm font-medium text-gray-500">Users Affected</dt>
                  <dd className="mt-1 text-sm text-gray-900">{data.issueDetails.userCount}</dd>
                </div>
                <div className="sm:col-span-1">
                  <dt className="text-sm font-medium text-gray-500">Culprit</dt>
                  <dd className="mt-1 text-sm text-gray-900">{data.issueDetails.culprit}</dd>
                </div>
                <div className="sm:col-span-1">
                  <dt className="text-sm font-medium text-gray-500">Assigned To</dt>
                  <dd className="mt-1 text-sm text-gray-900">{data.issueDetails.assignedTo || 'Unassigned'}</dd>
                </div>
                <div className="sm:col-span-2">
                  <dt className="text-sm font-medium text-gray-500">Permalink</dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    <a 
                      href={data.issueDetails.permalink} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-800"
                    >
                      View in Sentry
                    </a>
                  </dd>
                </div>
              </dl>
            </div>
            
            <div className="border-t border-gray-200 px-4 py-5 sm:px-6">
              <h3 className="text-lg font-medium text-gray-900">Actions</h3>
              <div className="mt-4 flex flex-wrap gap-2">
                {data.issueDetails.status === 'unresolved' ? (
                  <Form method="post">
                    <input type="hidden" name="action" value="resolve" />
                    <input type="hidden" name="issueId" value={data.issueDetails.id} />
                    <button
                      type="submit"
                      className="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50"
                      disabled={isSubmitting}
                    >
                      {isSubmitting && navigation.formData?.get('action') === 'resolve' ? 'Resolving...' : 'Resolve'}
                    </button>
                  </Form>
                ) : (
                  <Form method="post">
                    <input type="hidden" name="action" value="unresolve" />
                    <input type="hidden" name="issueId" value={data.issueDetails.id} />
                    <button
                      type="submit"
                      className="px-3 py-1 bg-yellow-500 text-white rounded hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-opacity-50"
                      disabled={isSubmitting}
                    >
                      {isSubmitting && navigation.formData?.get('action') === 'unresolve' ? 'Unresolving...' : 'Unresolve'}
                    </button>
                  </Form>
                )}
                
                <Form method="post" className="flex items-center gap-2">
                  <input type="hidden" name="action" value="assign" />
                  <input type="hidden" name="issueId" value={data.issueDetails.id} />
                  <input
                    type="text"
                    name="assignee"
                    placeholder="Email address"
                    value={assigneeInput}
                    onChange={(e) => setAssigneeInput(e.target.value)}
                    className="px-3 py-1 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                  />
                  <button
                    type="submit"
                    className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                    disabled={isSubmitting}
                  >
                    {isSubmitting && navigation.formData?.get('action') === 'assign' ? 'Assigning...' : 'Assign'}
                  </button>
                </Form>
              </div>
            </div>
            
            <div className="border-t border-gray-200 px-4 py-5 sm:px-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Latest Event</h3>
              {data.issueDetails.events && data.issueDetails.events.length > 0 && (
                <div>
                  <div className="mb-4">
                    <h4 className="text-md font-medium text-gray-700">Event Information</h4>
                    <div className="mt-2 grid grid-cols-1 gap-x-4 gap-y-2 sm:grid-cols-2">
                      <div className="sm:col-span-1">
                        <span className="text-sm font-medium text-gray-500">Timestamp:</span>
                        <span className="ml-2 text-sm text-gray-900">{formatDate(data.issueDetails.events[0].timestamp)}</span>
                      </div>
                      <div className="sm:col-span-1">
                        <span className="text-sm font-medium text-gray-500">User:</span>
                        <span className="ml-2 text-sm text-gray-900">{data.issueDetails.events[0].user.email}</span>
                      </div>
                      <div className="sm:col-span-1">
                        <span className="text-sm font-medium text-gray-500">IP Address:</span>
                        <span className="ml-2 text-sm text-gray-900">{data.issueDetails.events[0].user.ip_address}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="mb-4">
                    <h4 className="text-md font-medium text-gray-700">Tags</h4>
                    <div className="mt-2 flex flex-wrap gap-2">
                      {Object.entries(data.issueDetails.events[0].tags).map(([key, value]) => (
                        <span key={key} className="px-2 py-1 text-xs bg-gray-100 rounded-full">
                          {key}: {value}
                        </span>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="text-md font-medium text-gray-700">Exception</h4>
                    <div className="mt-2">
                      <div className="text-sm">
                        <span className="font-medium">{data.issueDetails.events[0].exception.values[0].type}:</span>
                        <span className="ml-1">{data.issueDetails.events[0].exception.values[0].value}</span>
                      </div>
                      
                      <div className="mt-2">
                        <h5 className="text-sm font-medium text-gray-700">Stack Trace</h5>
                        <div className="mt-1 bg-gray-50 p-4 rounded overflow-auto max-h-96">
                          {data.issueDetails.events[0].exception.values[0].stacktrace.frames.map((frame, index) => (
                            <div key={index} className="text-xs font-mono mb-1">
                              at {frame.function} ({frame.filename}:{frame.lineno}:{frame.colno})
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      ) : (
        // Issues list view
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {data.issues && data.issues.length > 0 ? (
              data.issues.map((issue) => (
                <li key={issue.id} className="px-6 py-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center">
                        <button
                          onClick={() => window.location.href = `/admin/system/errors?issueId=${issue.id}`}
                          className="font-medium text-blue-600 hover:text-blue-800"
                        >
                          {issue.title}
                        </button>
                        <span className={`ml-2 px-2 py-1 text-xs rounded-full ${getLevelColor(issue.level)}`}>
                          {issue.level}
                        </span>
                        <span className={`ml-2 px-2 py-1 text-xs rounded-full ${getStatusColor(issue.status)}`}>
                          {issue.status}
                        </span>
                      </div>
                      <div className="mt-1 text-sm text-gray-500">
                        {issue.culprit} • {formatDate(issue.lastSeen)} • {issue.count} occurrences • {issue.userCount} users affected
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      {issue.status === 'unresolved' ? (
                        <Form method="post">
                          <input type="hidden" name="action" value="resolve" />
                          <input type="hidden" name="issueId" value={issue.id} />
                          <button
                            type="submit"
                            className="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50"
                            disabled={isSubmitting}
                          >
                            {isSubmitting && navigation.formData?.get('issueId') === issue.id && navigation.formData?.get('action') === 'resolve'
                              ? 'Resolving...' 
                              : 'Resolve'}
                          </button>
                        </Form>
                      ) : (
                        <Form method="post">
                          <input type="hidden" name="action" value="unresolve" />
                          <input type="hidden" name="issueId" value={issue.id} />
                          <button
                            type="submit"
                            className="px-3 py-1 bg-yellow-500 text-white rounded hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-opacity-50"
                            disabled={isSubmitting}
                          >
                            {isSubmitting && navigation.formData?.get('issueId') === issue.id && navigation.formData?.get('action') === 'unresolve'
                              ? 'Unresolving...' 
                              : 'Unresolve'}
                          </button>
                        </Form>
                      )}
                    </div>
                  </div>
                </li>
              ))
            ) : (
              <li className="px-6 py-4 text-center text-gray-500">No issues found</li>
            )}
          </ul>
        </div>
      )}
    </div>
  );
}